import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

public class Code {
    public static String getEncryptedMarkCode(String markCode) {
        String deCodeStr = sha256(sha256(markCode)).toUpperCase();
        return deCodeStr;
    }

    private static String sha256(String markCode) {
        MessageDigest md = null;
        String strDes = null;
        byte[] bt = markCode.getBytes();
        try {
            md = MessageDigest.getInstance("SHA-256");
            md.update(bt);
            strDes = bytes2Hex(md.digest());
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
        return strDes;
    }

    private static String bytes2Hex(byte[] bts) {
        String des = "";
        String tmp = null;
        for (int i = 0; i < bts.length; i++) {
            tmp = Integer.toHexString(bts[i] & 0xFF);
            if (tmp.length() == 1)
                des = des + "0";
            des = des + tmp;
        }
        return des;
    }

    public static void main(String[] arg0) {
        List<String> codes = new ArrayList();
        codes.add("234232239961637916");
        codes.add("133000200000000164");
        codes.add("133000200000000165");
        codes.add("133000200000000166");
        codes.add("133000200000000167");
        codes.add("133000200000000168");
        codes.add("133000200000000169");
        codes.add("133000200000000170");
        codes.add("133000200000000150");
        codes.add("133000200000000151");
        codes.add("133000200000000152");
        codes.add("133000200000000153");
        codes.add("133000200000000154");
        codes.add("134000200000000000");
        codes.add("134000200000000001");
        codes.add("134000200000000002");
        codes.add("134000200000000003");
        codes.add("134000200000000030");
        codes.add("133000200000000104");
        codes.add("133000200000000105");
        codes.add("133000200000000106");
        codes.add("133000200000000107");
        codes.add("133000200000000108");
        codes.add("133000200000000109");
        codes.add("133000200000000110");
        codes.add("133000200000000111");
        codes.add("133000200000000091");
        codes.add("133000200000000064");
        codes.add("133000200000000065");
        codes.add("133000200000000066");
        codes.add("133000200000000067");
        codes.add("133000200000000068");
        codes.add("133000200000000069");
        codes.add("133000200000000070");
        codes.add("134000200000000020");
        codes.add("134000200000000021");
        codes.add("134000200000000012");
        codes.add("134000200000000014");
        codes.add("134000200000000015");
        for (String code : codes){
            String encryptedMarkCode = getEncryptedMarkCode(code);
            System.out.println(code + " : " + encryptedMarkCode);
        }

    }
}
