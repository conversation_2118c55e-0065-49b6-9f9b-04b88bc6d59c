package tpm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * Author: linmj
 * Date: 2024/3/4 16:17
 */

@Data
@ToString
public class AIRewardRuleDTO {

    private List<SkuCountRuleDTO> skuCountRules;

    private List<ContinuesSkuFacingCountRuleDTO> continuesSkuFacingCountRules;

    public static void main(String[] args) {

        AIRewardRuleDTO dto = new AIRewardRuleDTO();
        dto.setSkuCountRules(Lists.newArrayList());
        SkuCountRuleDTO skuCountRuleDTO = new SkuCountRuleDTO();
        skuCountRuleDTO.setSkuCount(1);
        skuCountRuleDTO.setSkuRanges(Lists.newArrayList(new SimpleObjectDTO("饮料类", "123", null),new SimpleObjectDTO("白奶", "123", null)));
        dto.getSkuCountRules().add(skuCountRuleDTO);
        ContinuesSkuFacingCountRuleDTO continuesSkuFacingCountRuleDTO = new ContinuesSkuFacingCountRuleDTO();
        continuesSkuFacingCountRuleDTO.setFocusSkuCount(20);
        continuesSkuFacingCountRuleDTO.setSkuFacingRatios(Lists.newArrayList());
        ContinuesSkuFacingCountRuleDTO.SkuFacingRatioDTO skuFacingRatioDTO = new ContinuesSkuFacingCountRuleDTO.SkuFacingRatioDTO();
        skuFacingRatioDTO.setSkuRanges(Lists.newArrayList(new SimpleObjectDTO("优酸乳类", "321", null),new SimpleObjectDTO("白奶", "123", null)));
        skuFacingRatioDTO.setRatio(50.0);
        continuesSkuFacingCountRuleDTO.getSkuFacingRatios().add(skuFacingRatioDTO);
        dto.setContinuesSkuFacingCountRules(Lists.newArrayList(continuesSkuFacingCountRuleDTO));
        System.out.println(JSON.toJSONString(dto));
    }
}
