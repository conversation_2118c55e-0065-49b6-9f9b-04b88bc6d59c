package proxy;

import proxy.Dog;
import proxy.dynamic.ProxyHandler;
import proxy.statics.MotiveProxy;

import java.lang.reflect.Proxy;

/**
 * Author: linmj
 * Date: 2023/6/14 16:42
 */
public class Test {


    public static void main(String[] args) {
        Dog dog = new Dog();
        /*MotiveProxy proxy = new MotiveProxy(dog);
        proxy.eat();*/
        Motive newDog = (Motive) Proxy.newProxyInstance(dog.getClass().getClassLoader(), dog.getClass().getInterfaces(), new ProxyHandler(dog));
        newDog.eat();
    }
}
