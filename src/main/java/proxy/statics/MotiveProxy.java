package proxy.statics;

import proxy.Motive;

/**
 * Author: linmj
 * Date: 2023/6/14 16:40
 */
public class MotiveProxy implements Motive {


    private Motive motive;


    public MotiveProxy(Motive motive){
        this.motive =motive;
    }

    @Override
    public void eat() {
        System.out.println("proxy motive start");
        motive.eat();
        System.out.println("proxy motive end");
    }
}
