package guice;

import com.google.inject.AbstractModule;
import com.google.inject.Singleton;
import com.google.inject.name.Names;
import guice.provider.ComplexServiceProvider;
import guice.service.ComplexService;
import guice.service.MongoUserServiceImpl;
import guice.service.PGUserServiceImpl;
import guice.service.UserService;

public class DemoModule extends AbstractModule {

    @Override
    protected void configure() {
        super.configure();
        bind(UserService.class).to(PGUserServiceImpl.class).in(Singleton.class);
        bind(UserService.class).annotatedWith(Names.named("mongo")).toInstance(new MongoUserServiceImpl());
        bind(ComplexService.class).toProvider(ComplexServiceProvider.class).in(Singleton.class);
        bindConstant().annotatedWith(Names.named("param")).to("123");
    }
}
