package guice.processor;

import guice.service.ComplexService;
import guice.service.UserService;

import javax.inject.Inject;
import javax.inject.Named;

public class DemoProcessor {

    private final UserService pguUserService;

    private final UserService mongoUserService;

    private final ComplexService complexService;

    private final String param;

    @Inject
    public DemoProcessor(UserService pguUserService, @Named("mongo") UserService mongoUserService, ComplexService complexService, @Named("param") String param) {
        this.pguUserService = pguUserService;
        this.mongoUserService = mongoUserService;
        this.complexService = complexService;
        this.param = param;
    }


    public String complex() {
        return complexService.complex()+":" + complexService.complex2(param);
    }

    public String hello() {
        return pguUserService.hello();
    }

    public String hello2() {
        return mongoUserService.hello();
    }
}
