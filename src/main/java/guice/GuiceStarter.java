package guice;

import com.google.inject.Guice;
import com.google.inject.Injector;
import guice.processor.DemoProcessor;

public class GuiceStarter {

    public static void main(String[] args) {
        Injector injector = Guice.createInjector(new DemoModule());

        DemoProcessor demoProcessor = injector.getInstance(DemoProcessor.class);

        System.out.println(demoProcessor.complex());
        System.out.println(demoProcessor.hello());
        System.out.println(demoProcessor.hello2());
    }
}
