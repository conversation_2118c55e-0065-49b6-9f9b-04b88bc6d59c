import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
// import com.fxiaoke.common.http.handler.TimeoutSettings;
// import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@SuppressWarnings("Duplicates")
public class HttpUtil {

    private HttpUtil() {
    }


    private static final String CONFIG_NAME = "fs-fmcg-sdk-apis";

    private static final String ENTERPRISE_ID_HEADER = "x-fs-ei";
    private static final String EMPLOYEE_ID_HEADER = "x-fs-userInfo";
    private static final String TRACE_ID_HEADER = "x-fs-trace-id";
    public static final long CONNECT_TIME_OUT = 5L;
    public static final long IO_TIME_OUT = 30L;

    private static final String EMPTY_BODY = "{}";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType URL_ENCODE_MEDIA_TYPE = MediaType.parse("application/x-www-form-urlencoded");
    private static final OkHttpClient OK_HTTP_CLIENT;
    private static final List<Proxy> PROXY_LIST = Lists.newArrayList(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("egress-proxy.egress", 9999)));
    private static final List<Proxy> NO_PROXY = Lists.newArrayList(Proxy.NO_PROXY);
    private static final List<String> NO_PROXY_HOSTS = Lists.newArrayList(".foneshare", ".intranet.fxiaoke.com", ".fxiaokeyun", ".firstshare", ".baidubce");

    static {

        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(500);
        dispatcher.setMaxRequestsPerHost(200);
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIME_OUT, TimeUnit.SECONDS)
                .readTimeout(IO_TIME_OUT, TimeUnit.SECONDS)
                .writeTimeout(IO_TIME_OUT, TimeUnit.SECONDS);

        OK_HTTP_CLIENT = builder.build();

    }

    public static <T, R> R multiFromFilePost(String url, Map<String, Object> headers, Map<String, Object> othersParams, String key, String fileName, byte[] bytes, Class<R> clazz) {
        Request.Builder builder = new Request.Builder();
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((k, v) -> {
                if (!Objects.isNull(v)) {
                    builder.addHeader(k, v.toString());
                }
            });
        }
        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), bytes);
        MultipartBody.Builder requestBodyBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart(key, fileName, fileBody);
        if (MapUtils.isNotEmpty(othersParams)) {
            for (Map.Entry<String, Object> entry : othersParams.entrySet()) {
                requestBodyBuilder.addFormDataPart(entry.getKey(), entry.getValue().toString());
            }
        }
        RequestBody requestBody = requestBodyBuilder.build();
        Request request = builder
                .url(url)
                .post(requestBody)
                .build();
        try {
            Response response = OK_HTTP_CLIENT.newCall(request).execute();
            if (response.code() != 200) {
                throw new RuntimeException(response.message());
            }
            if (response.body() == null) {
                throw new RuntimeException("fail");
            }
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            jsonObject.put("headers", response.headers().toMultimap());
            return jsonObject.toJavaObject(clazz);
        } catch (IOException ex) {
            throw new RuntimeException("fail");
        }
    }


    public static <T, R> R post(String url, Map<String, Object> headers, T arg, Class<R> clazz) {
        return post(url, headers, arg, clazz, null);
    }

    public static <T, R> R post(String url, Map<String, Object> headers, T arg, Class<R> clazz, Object timeoutSettings) {
        System.out.println("url:" + url);
        System.out.println("arg:" + JSONObject.toJSONString(arg));
        System.out.println("header:" + JSONObject.toJSONString(headers));
        Request.Builder builder = new Request.Builder();
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((k, v) -> {
                if (!Objects.isNull(v)) {
                    builder.addHeader(k, v.toString());
                }
            });
        }
        String argJson = arg == null ? EMPTY_BODY : JSON.toJSONString(arg);
        Request request = builder
                .url(url)
                .post(RequestBody.create(JSON_MEDIA_TYPE, argJson))
                .build();
        try {
            Response response = OK_HTTP_CLIENT.newCall(request).execute();
            if (response.code() / 100 != 2) {
                throw new RuntimeException(response.message());
            }
            if (response.body() == null) {
                throw new RuntimeException("fail");
            }
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            jsonObject.put("headers", response.headers().toMultimap());
            return jsonObject.toJavaObject(clazz);
        } catch (IOException ex) {
            throw new RuntimeException("fail");
        }
    }


    public static <R> R get(String url, Map<String, Object> headers, Class<R> clazz) {
        System.out.println("url:" + url);
        System.out.println("header:" + JSONObject.toJSONString(headers));
        Request.Builder builder = new Request.Builder();
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((k, v) -> {
                if (!Objects.isNull(v)) {
                    builder.addHeader(k, v.toString());
                }
            });
        }

        Request request = builder.url(url).get().build();
        try {
            Response response = OK_HTTP_CLIENT.newCall(request).execute();
            if (response.code() != 200) {
                throw new RuntimeException(response.message());
            }
            if (response.body() == null) {
                throw new RuntimeException("fail");
            }
            return JSON.parseObject(response.body().string(), clazz);
        } catch (IOException ex) {
            throw new RuntimeException("fail");
        }
    }
}