package thread;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class TestCompeletableSTask {


    public static void main(String[] args) throws ExecutionException, InterruptedException {
        CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
            return "Hello, World!";
        }).thenAccept(result -> {
            System.out.println(result);  // 输出: Hello, World!
        });

        future.get();
    }
}
