import com.beust.jcommander.internal.Lists;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.google.common.util.concurrent.RateLimiter;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Main {

  /*  public static final ScheduledExecutorService scheduledExecutorService;*/

    private static final Pattern SCAN_CODE_PATTERN = Pattern.compile("\\d{18}");

    static {
      /*  AtomicInteger count = new AtomicInteger(0);
        scheduledExecutorService = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("retry-task-distribute-" + count.incrementAndGet());
            return thread;
        });*/

    }

    public static void main(String[] args) throws InterruptedException {
        System.out.println(UUID.randomUUID().toString().replace("-",""));

        System.out.println(sign(Lists.newArrayList("TIDAAtLh", "test001", "813f0f836c0e4f45a927bb027e76216b", "1.0.0", "tx016f9d5f179844711a48e7342eee1b", "30f513d8f804417c933e41c372e95ccb"),"J3mxva223coMdoEqfe7kT0Nn5E4bLEQd7hKdFyPH4zUg5tDJBltgC5N1AFg5MEbn"));

    }

    public static String sign(List<String> values, String ticket) { //values传ticket外的其他参数
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);


        StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        System.out.println(sb);
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
    }




}