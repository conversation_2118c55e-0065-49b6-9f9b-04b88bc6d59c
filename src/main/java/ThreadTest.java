import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Author: linmj
 * Date: 2023/6/5 15:01
 */
public class ThreadTest {

    private static Semaphore semaphoreA = new Semaphore(1);
    private static Semaphore semaphoreB = new Semaphore(0);
    private static Semaphore semaphoreC = new Semaphore(0);

    public static void main(String[] args) throws InterruptedException {

        Object a = new Object();
        Object b = new Object();
        Object c = new Object();
        a.wait();

        Thread threadA = new Thread(() -> {
            try {
                while (true) {

                    semaphoreA.acquire();
                    System.out.println("A");
                    semaphoreB.release();

                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

        Thread threadB = new Thread(() -> {
            try {
                while (true) {

                    semaphoreB.acquire();
                    System.out.println("B");
                    semaphoreC.release();

                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

        Thread threadC = new Thread(() -> {
            try {
                while (true) {

                    semaphoreC.acquire();
                    System.out.println("C");
                    semaphoreA.release();

                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });

        threadA.start();
        threadB.start();
        threadC.start();


    }
}
