import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

/**
 * Author: linmj
 * Date: 2023/5/29 18:31
 */
public class MethodTest {


    public static void main(String[] args) {
        long ts = new Date().getTime();
        String clientID = "QDRHPT";
        String secret = "34829EDA-D347-4FDE-A835-4CB00C08DC5B";
        String md5 = md52(clientID + secret + ts).toUpperCase();
        System.out.println(ts);
        System.out.println(md5);
    }

    public static String md52(String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashedBytes = md.digest(content.getBytes());
            // 将哈希值转换为十六进制表示
            StringBuilder sb = new StringBuilder();
            for (byte b : hashedBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {

        }
        return null;
    }


    private static String md5(String plaintext) {
        try {
            // 创建MD5消息摘要对象
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将字符串转换为字节数组
            byte[] originalBytes = plaintext.getBytes(StandardCharsets.UTF_8);

            // 计算MD5摘要
            byte[] digestBytes = md.digest(originalBytes);

            // 将摘要转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digestBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            // 打印加密后的结果
            return hexString.toString().toUpperCase();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }
}
