package es;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch.core.IndexRequest;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.TransportUtils;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import es.model.User;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;

import javax.net.ssl.SSLContext;
import java.io.IOException;

/**
 * Author: linmj
 * Date: 2023/4/14 18:05
 */
public class ESClient {

    private static String host = "127.0.0.1";
    private static int port = 9200;

    private static String login = "elastic";

    private static String password = "WWYRSgj9-LBSHdOvZvK1";
    public static void main(String[] args) throws IOException {

        String fingerprint = "3b060b8ab2db66631786fb288943898014aeb32e4cdf1ffa4a69663c962200d3";

        SSLContext sslContext = TransportUtils
                .sslContextFromCaFingerprint(fingerprint);

        BasicCredentialsProvider credsProv = new BasicCredentialsProvider();
        credsProv.setCredentials(
                AuthScope.ANY, new UsernamePasswordCredentials(login, password)
        );

        RestClient restClient = RestClient
                .builder(new HttpHost(host, port, "https"))
                .setHttpClientConfigCallback(hc -> hc
                        .setSSLContext(sslContext)
                        .setDefaultCredentialsProvider(credsProv)
                )
                .build();


        ElasticsearchTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper());


        ElasticsearchClient client = new ElasticsearchClient(transport);

       /* User user = new User();
        user.setName("123");
        user.setId("1");
        IndexRequest<User> request = new IndexRequest.Builder<User>().index("user").document(user).build();

        Result result = client.index(request).result();
        System.out.println(result.jsonValue());*/

        System.out.println(client.get(g->g.index("user").id("3OgBjocBvdPSRiWwHB1L"),User.class));
        System.out.println(client.count().count());
        client.shutdown();
    }
}
