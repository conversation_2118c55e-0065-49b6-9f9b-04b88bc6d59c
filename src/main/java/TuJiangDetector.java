import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;

/**
 * Author: linmj
 * Date: 2024/5/20 11:24
 */

@Component(value = "tuJiangDetector")
public class TuJiangDetector {


    public void detect(String token, byte[] imageBytes) {
        String host = "http://uat.api.imagedt.com";
        String openApiHost = "http://uat.openapi.imagedt.com";
        String storeCode = "M001";
        String businessId = UUID.randomUUID().toString();
        String customKey = "mengniufx";
        String userId = "1.1";
        String storeId = "abc";
        String path = "mockPath";

        long detectStartTime = System.currentTimeMillis();

        createAction(openApiHost, businessId, storeCode, token, userId, storeId);
        uploadFile(host, businessId, customKey, path, token, imageBytes, path);
        finishAction(openApiHost, businessId, token);
        JSONObject detectResult = getReport(token, openApiHost, businessId);

        try {
            JSONObject detectInfo = detectResult.getJSONObject("mengniucwfx");
            System.out.println(detectInfo);

        } catch (Exception e) {
            throw e;
        } finally {
        }


    }

    private List<String> createAction(String host, String businessId, String storeCode, String token, String userId, String accountId) {
        String url = host + "/visit-tenant/v1/action";
        JSONObject params = new JSONObject();
        params.put("businessId", businessId);
        params.put("storeCode", storeCode);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Idt-Token", token);
        JSONObject extraData = new JSONObject();
        extraData.put("userId", userId);
        extraData.put("storeId", accountId);
        params.put("extProperties", extraData.toJSONString());




        JSONObject result = HttpUtil.post(url, headers, params, JSONObject.class);
        return getResultData(result).getJSONArray("customKeys").toJavaList(String.class);
    }

    private void uploadFile(String host, String businessId, String customKey, String nPath, String token, byte[] imageBytes, String imagePath) {

        String url = host + "/content/tenant/upload";

        Map<String, Object> headers = new HashMap<>();
        headers.put("Idt-Token", token);

        JSONObject params = new JSONObject();
        params.put("businessId", businessId);
        params.put("customKey", customKey);
        params.put("md5", SecretUtil.md5(imageBytes));
        System.out.println(SecretUtil.md5(imageBytes));
        JSONObject extraData = new JSONObject();
        extraData.put("imagePath", imagePath);
        params.put("extProperties", extraData.toJSONString());
        JSONObject result = HttpUtil.multiFromFilePost(url, headers, params, "file", nPath + ".jpg", imageBytes, JSONObject.class);
        JSONObject data = getResultData(result);
    }

    private JSONObject getResultData(JSONObject result) {
        if ("0".equals(result.getString("code"))) {
            return result.getJSONObject("data");
        } else if ("900401".equals(result.getString("code")) || "094001".equals(result.getString("code"))) {
            throw new RuntimeException("token过期");
        } else {
            throw new RuntimeException("900402");
        }
    }

    private JSONObject getReport(String token, String host, String businessId) {
        String url = host + "/discern-query/report/businessId/keycode";

        Map<String, Object> headers = new HashMap<>();
        headers.put("Idt-Token", token);

        JSONObject params = new JSONObject();
        params.put("businessId", businessId);
        params.put("keyCode", "mengniu_fx");
        String paramsStr = SecretUtil.formDataUrlParams(params);
        url = url + "?" + paramsStr;
        long startTime = System.currentTimeMillis();
        int maxCount = 40;
        int timeInterval = 400;
        int count = 0;
        while (count < maxCount) {
            count++;
            JSONObject result = HttpUtil.get(url, headers, JSONObject.class);
            String code = result.getString("code");
            if ("0".equals(code)) {
                return result.getJSONObject("data");
            } else if ("900006".equals(code) || "900005".equals(code)) {
                try {
                    Thread.sleep(timeInterval);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            } else if ("900401".equals(result.getString("code")) || "094001".equals(result.getString("code"))) {
                throw new RuntimeException("token过期");
            } else {
                throw new RuntimeException("请求第三方失败");
            }
        }
        throw new RuntimeException("获取图匠识别报告超时，请稍后再试。businessId:" + businessId);
    }


    private void finishAction(String host, String businessId, String token) {
        String url = host + "/visit-tenant/v1/action/finish";
        JSONObject params = new JSONObject();
        params.put("businessId", businessId);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Idt-Token", token);

        JSONObject result = HttpUtil.post(url, headers, params, JSONObject.class);
        getResultData(result);
    }


    public static void main(String[] args) {
        TuJiangDetector detector = new TuJiangDetector();
        String token = "468fe55e-ce35-4ea0-a35c-c277ea557cf3";
        //todo:替换对应货架图片地址即可
        byte[] image = getImageByte("/Users/<USER>/Coder/Project/Idea/RandomProgramming/RandomProgramming/src/main/resources/货架2.jpg");
        detector.detect(token, image);
    }

    private static byte[] getImageByte(String path) {
        File file = new File(  path);
        byte[] bytes = new byte[(int) file.length()];
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            int flag = fis.read(bytes);

        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return bytes;
    }
}
