import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/8/21 18:30
 */
public class SecretUtil {


    public static String md5(String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashedBytes = md.digest(content.getBytes());
            // 将哈希值转换为十六进制表示
            StringBuilder sb = new StringBuilder();
            for (byte b : hashedBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
        }
        return null;
    }

    public static String md5(byte[] content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashedBytes = md.digest(content);
            // 将哈希值转换为十六进制表示
            StringBuilder sb = new StringBuilder();
            for (byte b : hashedBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
        }
        return null;
    }

    public static String formDataUrlParams(Map<String,Object> params){
        StringBuilder paramsBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            try {
                paramsBuilder.append(key).append('=').append(URLEncoder.encode(value.toString(), "utf-8"));
                paramsBuilder.append('&');
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException( e.getMessage());
            }
        }
        return paramsBuilder.substring(0, paramsBuilder.length() - 1);
    }


    public static void main(String[] args) {

    }


}
