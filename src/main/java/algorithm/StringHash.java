package algorithm;

/**
 * Author: linmj
 * Date: 2023/4/17 10:32
 */
public class StringHash {


    private int base;

    private int modBase;

    private char baseChar;

    private long hash[];

    public StringHash(int base, int modBase, char baseChar) {
        this.base = base;
        this.modBase = modBase;
        this.baseChar = baseChar;
    }

    public long hash(String code) {
        long hash = 0;
        for (int i = 0; i < code.length(); i++) {
            char c = code.charAt(i);
            hash = (hash * base + c - baseChar) % modBase;
        }
        return hash;
    }

}
