import com.alibaba.fastjson.JSON;

/**
 * Author: linmj
 * Date: 2023/3/24 17:22
 */
public class StreamChecker {

    private static final char SIGN = (char) ('z' + 1);

    class Trie {
        char alp;
        Trie[] sons = new Trie[27];

        Trie(char alp) {
            this.alp = alp;
        }
    }

    private Trie root;

    public StreamChecker(String[] words) {
        root = new Trie(SIGN);
        for (String word : words) {
            set(word,0);
        }
    }

    public boolean query(char letter) {

        return false;
    }

    private void set(String word,int index){

    }

    public static void main(String[] args) {
        System.out.println(JSON.parseObject("dfd",String.class));
    }
}
