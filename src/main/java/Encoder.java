import lombok.extern.slf4j.Slf4j;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.io.*;
import java.security.AccessController;
import java.security.NoSuchAlgorithmException;
import java.security.PrivilegedAction;
import java.util.Base64;


public class Encoder {
    public static void main(String[] args) throws Exception {


        System.out.println(randomKey("AES"));
    }

    public static String symmetricalEncode(String content, String key) {

        return null;
    }

    public static String symmetricalDecode(String content, String key) {
        return null;
    }

    public static String randomKey(String algorithm) {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(algorithm);
            return formKeyString(keyGenerator.generateKey());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private static SecretKey getSecretKey(String key) {
        try {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(key.getBytes());
            ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);
            return (SecretKey) objectInputStream.readObject();
        } catch (Exception e) {
        }
        return null;
    }

    // 保存密钥到文件
    private static void saveKeyToFile(SecretKey secretKey, String filename) {
        FileOutputStream outputStream = null;
        ObjectOutputStream objectOutputStream = null;
        try {
            outputStream = new FileOutputStream(filename);
            objectOutputStream = new ObjectOutputStream(outputStream);
            objectOutputStream.writeObject(secretKey);
            objectOutputStream.close();
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    // 从文件中加载密钥
    private static SecretKey loadKeyFromFile(String filename) throws IOException, ClassNotFoundException {
        FileInputStream inputStream = new FileInputStream(filename);
        ObjectInputStream objectInputStream = new ObjectInputStream(inputStream);
        SecretKey secretKey = (SecretKey) objectInputStream.readObject();
        objectInputStream.close();
        inputStream.close();
        return secretKey;
    }

    private static String formKeyString(SecretKey secretKey) {
        String tmpFileName = "tempFileName" + System.currentTimeMillis() + Math.random() + ".key";
        saveKeyToFile(secretKey, tmpFileName);
        File file = new File(tmpFileName);
        try {
            FileInputStream inputStream = new FileInputStream(file);
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes);
            String keyString = Base64.getEncoder().encodeToString(bytes);
            AccessController.doPrivileged((PrivilegedAction<Boolean>) file::delete);
            return keyString;
        } catch (Exception e) {
        }
        return null;
    }
}
